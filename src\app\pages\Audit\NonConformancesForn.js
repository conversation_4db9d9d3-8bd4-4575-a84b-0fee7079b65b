import React, { useEffect, useState } from "react";
import { DropzoneArea } from 'material-ui-dropzone';
import axios from "axios";
import API from "../../services/API";
import { AUDIT_FINDINGS_ASSIGN_ACTION_URL, AUDIT_GMS3_URL, AUDIT_WITH_ID_URL, GET_USERS_BY_ROLE, INSPECTION_ACTION_PLAN_REVIEWER, STATIC_URL, AUDIT_GMS3_WITH_ID_URL, AUDIT_GMS2_WITH_ID_URL } from "../../constants";
import { processUploadedFiles, getAcceptedFileTypes } from "./utils/imageUtils";

const NonConformancesForm = ({ selectedAuditGMSOne, gms, aformData, handleChange, handleFileChange, auditId, selectedAuditGmsThree, selectedCategory, handleCategoryChange }) => {

    // Form fields for Non-Conformances
    const [audit, setAudit] = useState(null);
    const [users, setUsers] = useState([])
    const [dueDate, setDueDate] = useState(new Date())
    const [selectedUser, setSelecedUser] = useState('')
    const [file, setFile] = useState([])
    const [gmsThreeData, setGmsThreeData] = useState(null)
    const [gmsTwoTitle, setGmsTwoTitle] = useState('')
    const [isProcessingFiles, setIsProcessingFiles] = useState(false)

    // Fetch GMS 3 data and then GMS 2 title
    useEffect(() => {
        const fetchGmsTwoTitle = async () => {
            if (selectedAuditGmsThree) {
                try {
                    // Get GMS 3 data
                    const gmsThreeResponse = await API.get(AUDIT_GMS3_WITH_ID_URL(selectedAuditGmsThree));
                    if (gmsThreeResponse.status === 200) {
                        const gmsThreeItem = gmsThreeResponse.data;
                        setGmsThreeData(gmsThreeItem);

                        // Get GMS 2 data using auditGmsTwoId
                        if (gmsThreeItem.auditGmsTwoId) {
                            const gmsTwoResponse = await API.get(AUDIT_GMS2_WITH_ID_URL(gmsThreeItem.auditGmsTwoId));
                            if (gmsTwoResponse.status === 200) {
                                setGmsTwoTitle(gmsTwoResponse.data.name);
                            }
                        }
                    }
                } catch (error) {
                    console.error('Error fetching GMS data:', error);
                }
            }
        };

        fetchGmsTwoTitle();
    }, [selectedAuditGmsThree]);

    useEffect(() => {
        if (audit) {
            getActionImplementors();
        }
    }, [audit])
    const fetchImageFile = async (url) => {
        const response = await fetch(url);
        const blob = await response.blob();
        const file = new File([blob], url.split('/').pop(), { type: blob.type });
        console.log(file)
        return file;
    };
    useEffect(() => {

        const addFiles = async () => {
            const urls = aformData.uploads ? aformData.uploads.map(i => {
                return `${STATIC_URL}/${i}`
            }) : []
            const fetchedFiles = await Promise.all(urls.map(url => fetchImageFile(url)));
            console.log(fetchedFiles)
            setFile(fetchedFiles)

        }
        addFiles();

    }, [])


    useEffect(() => {
        const getAuditInfo = async () => {

            const response = await API.get(AUDIT_WITH_ID_URL(auditId))
            if (response.status === 200) {
                setAudit(response.data)
            }
        }
        getAuditInfo();
    }, [])

    const getActionImplementors = async () => {
        const response = await API.post(GET_USERS_BY_ROLE, { locationOneId: audit.locationOneId, locationTwoId: audit.locationOneId, locationThreeId: audit.locationOneId, locationFourId: audit.locationOneId, mode: 'audit-action-plan-implementor' });
        if (response.status === 200) {
            setUsers(response.data)
        }
    }

    // Custom file change handler that processes HEIC files
    const handleCustomFileChange = async (files) => {
        if (!files || files.length === 0) {
            handleFileChange(files);
            return;
        }

        try {
            setIsProcessingFiles(true);
            const processedFiles = await processUploadedFiles(files, 0.8);
            handleFileChange(processedFiles);
        } catch (error) {
            console.error('Error processing files:', error);
            alert('Error processing HEIC files. Please try again or use JPG/PNG format.');
        } finally {
            setIsProcessingFiles(false);
        }
    };






    return (
        <div className="audit-form-container">


            <form className="audit-form">
                {/* Header Section */}
                <div className="form-header mb-4">
                    <h2 className="form-title text-danger mb-2">
                        <i className="mdi mdi-alert-circle-outline me-2"></i>
                        {gmsTwoTitle || 'Non-Conformances Form'}
                    </h2>

                </div>

                {/* Category Selection Card */}
                <div className="form-card mb-4">
                    <div className="card-header">
                        <h5 className="card-title mb-0">
                            <i className="mdi mdi-tag-outline me-2"></i>
                            Category Selection
                        </h5>
                    </div>
                    <div className="card-body">
                        <div className="form-group mb-0">
                            <label htmlFor="categorySelect" className="form-label fw-semibold">
                                Category <span className="text-danger">*</span>
                            </label>
                            <select
                                className="form-select form-control-lg"
                                value={selectedCategory}
                                onChange={handleCategoryChange}
                                id="categorySelect"
                                required
                            >
                                <option value="">Select Category...</option>
                                <option value="Good Practices">✓ Good Practices</option>
                                <option value="Non-Conformances">⚠ Non-Conformances</option>
                                <option value="Opportunity For Improvement">💡 Opportunity For Improvement</option>
                            </select>
                            <p className="form-subtitle text-muted mb-0">
                                Document non-conformances and compliance issues requiring corrective action
                            </p>
                        </div>
                    </div>
                </div>



                {/* Findings Card */}
                <div className="form-card mb-4">
                    <div className="card-header">
                        <h5 className="card-title mb-0">
                            <i className="mdi mdi-text-box-outline me-2"></i>
                            Findings & Non-Conformance Details
                        </h5>
                    </div>
                    <div className="card-body">
                        <div className="form-group">
                            <label htmlFor="findings" className="form-label fw-semibold">
                                Detailed Findings <span className="text-danger">*</span>
                            </label>
                            <textarea
                                className="form-control form-control-lg"
                                id="findings"
                                name="findings"
                                onChange={handleChange}
                                value={aformData.findings}
                                rows="4"
                                placeholder="Describe the non-conformance in detail, including what was observed, which standards were not met, and the potential impact..."
                                required
                            ></textarea>
                            <div className="form-text">
                                <small className="text-muted">
                                    <i className="mdi mdi-information-outline me-1"></i>
                                    Provide specific details about the non-conformance and its implications
                                </small>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Classification Card */}
                <div className="form-card mb-4">
                    <div className="card-header">
                        <h5 className="card-title mb-0">
                            <i className="mdi mdi-alert-outline me-2"></i>
                            Severity Level <span className="text-danger">*</span>
                        </h5>
                    </div>
                    <div className="card-body">
                        <div className="form-group">
                            <label className="form-label fw-semibold">

                            </label>
                            <div className="classification-options">
                                <div className="form-check form-check-custom">
                                    <input
                                        className="form-check-input"
                                        type="radio"
                                        id="minor"
                                        name="classification"
                                        value="minor"
                                        onChange={handleChange}
                                        checked={aformData.classification === "minor"}
                                    />
                                    <label className="form-check-label" htmlFor="minor">
                                        <span className="severity-badge severity-minor">Minor</span>
                                        <small className="d-block text-muted mt-1">Low risk, minimal impact</small>
                                    </label>
                                </div>
                                <div className="form-check form-check-custom">
                                    <input
                                        className="form-check-input"
                                        type="radio"
                                        id="major"
                                        name="classification"
                                        value="major"
                                        onChange={handleChange}
                                        checked={aformData.classification === "major"}
                                    />
                                    <label className="form-check-label" htmlFor="major">
                                        <span className="severity-badge severity-major">Major</span>
                                        <small className="d-block text-muted mt-1">High risk, significant impact</small>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Evidence Upload Card */}
                <div className="form-card mb-4">
                    <div className="card-header">
                        <h5 className="card-title mb-0">
                            <i className="mdi mdi-camera-outline me-2"></i>
                            Evidence Documentation
                        </h5>
                    </div>
                    <div className="card-body">
                        <div className="form-group">
                            <label htmlFor="evidencePhotos" className="form-label fw-semibold">
                                Upload Photos (Evidence)
                                <small className="text-muted ms-2">(Max 5 files, 100MB each - Supports JPG, PNG, HEIC)</small>
                            </label>
                            <div className="upload-zone">
                                {isProcessingFiles && (
                                    <div className="processing-indicator">
                                        <i className="mdi mdi-loading mdi-spin me-2"></i>
                                        Converting HEIC files...
                                    </div>
                                )}
                                {file.length !== 0 ? (
                                    <DropzoneArea
                                        initialFiles={file}
                                        acceptedFiles={getAcceptedFileTypes().split(',')}
                                        dropzoneText="Drag and drop evidence images here or click to browse (JPG, PNG, HEIC supported)"
                                        filesLimit={5}
                                        maxFileSize={104857600}
                                        onChange={handleCustomFileChange}
                                        previewGridProps={{ container: { spacing: 2, direction: 'row', md: 12 } }}
                                        showPreviews={true}
                                        showPreviewsInDropzone={false}
                                        disabled={isProcessingFiles}
                                    />
                                ) : (
                                    <DropzoneArea
                                        acceptedFiles={getAcceptedFileTypes().split(',')}
                                        dropzoneText="Drag and drop evidence images here or click to browse (JPG, PNG, HEIC supported)"
                                        filesLimit={5}
                                        maxFileSize={104857600}
                                        onChange={handleCustomFileChange}
                                        previewGridProps={{ container: { spacing: 2, direction: 'row', md: 12 } }}
                                        showPreviews={true}
                                        showPreviewsInDropzone={false}
                                        disabled={isProcessingFiles}
                                    />
                                )}
                            </div>
                        </div>
                    </div>
                </div>



                {/* Standards & References Card */}
                <div className="form-card mb-4">
                    <div className="card-header">
                        <h5 className="card-title mb-0">
                            <i className="mdi mdi-book-outline me-2"></i>
                            Applicable Standards & References
                        </h5>
                    </div>
                    <div className="card-body">
                        <div className="form-group">

                            <input
                                type="text"
                                className="form-control form-control-lg"
                                id="standardsAndReferences"
                                name="standardsAndReferences"
                                onChange={handleChange}
                                value={aformData.standardsAndReferences}
                                placeholder="e.g., ISO 9001:2015, Company Policy XYZ, Procedure ABC..."
                            />
                            <div className="form-text">
                                <small className="text-muted">
                                    <i className="mdi mdi-information-outline me-1"></i>
                                    Reference the specific standards, policies, or procedures that were not followed
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
                {/* Potential Consequences Card */}
                <div className="form-card mb-4">
                    <div className="card-header">
                        <h5 className="card-title mb-0">
                            <i className="mdi mdi-alert-triangle-outline me-2"></i>
                            Potential Consequences
                        </h5>
                    </div>
                    <div className="card-body">
                        <div className="form-group">
                            <label htmlFor="potentialHazard" className="form-label fw-semibold">

                            </label>
                            <input
                                type="text"
                                className="form-control form-control-lg"
                                id="potentialHazard"
                                name="potentialHazard"
                                onChange={handleChange}
                                value={aformData.potentialHazard}
                                placeholder="Describe potential risks, safety hazards, or business impacts..."
                            />
                            <div className="form-text">
                                <small className="text-muted">
                                    <i className="mdi mdi-information-outline me-1"></i>
                                    Identify what could happen if this non-conformance is not addressed
                                </small>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Recommendations Card */}
                <div className="form-card mb-4">
                    <div className="card-header">
                        <h5 className="card-title mb-0">
                            <i className="mdi mdi-lightbulb-outline me-2"></i>
                            Recommended Mitigation Measures
                        </h5>
                    </div>
                    <div className="card-body">
                        <div className="form-group">
                            <label htmlFor="recommendations" className="form-label fw-semibold">

                            </label>
                            <textarea
                                className="form-control form-control-lg"
                                id="recommendations"
                                name="recommendations"
                                onChange={handleChange}
                                value={aformData.recommendations}
                                rows="3"
                                placeholder="Suggest specific actions to address the root cause of this non-conformance..."
                            ></textarea>
                        </div>
                    </div>
                </div>

                {/* Assignment & Timeline Card */}
                <div className="form-card mb-4">
                    <div className="card-header">
                        <h5 className="card-title mb-0">
                            <i className="mdi mdi-account-clock-outline me-2"></i>
                            Assignment & Timeline
                        </h5>
                    </div>
                    <div className="card-body">
                        <div className="row">
                            <div className="col-md-6">
                                <div className="form-group">
                                    <label className="form-label fw-semibold">
                                        Assign to User <span className="text-danger">*</span>
                                    </label>
                                    <select
                                        className="form-select form-control-lg"
                                        name="assignedToId"
                                        value={aformData.assignedToId}
                                        onChange={handleChange}
                                        required
                                    >
                                        <option value="">Select User...</option>
                                        {users.map(user => (
                                            <option value={user.id} key={user.id}>
                                                {user.firstName} {user.lastName}
                                            </option>
                                        ))}
                                    </select>
                                </div>
                            </div>
                            <div className="col-md-6">
                                <div className="form-group">
                                    <label className="form-label fw-semibold">
                                        Target Closure Date <span className="text-danger">*</span>
                                    </label>
                                    <input
                                        type="date"
                                        value={aformData.dueDate}
                                        name="dueDate"
                                        onChange={handleChange}
                                        className="form-control form-control-lg"
                                        min={new Date().toISOString().split('T')[0]}
                                        required
                                    />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </form>

            <style jsx>{`
                .audit-form-container {
                    max-width: 900px;
                    margin: 0 auto;
                    padding: 20px;
                }

                .form-header {
                    text-align: center;
                    border-bottom: 2px solid #e9ecef;
                    padding-bottom: 20px;
                }

                .form-title {
                    font-size: 1.75rem;
                    font-weight: 600;
                    color: #dc3545;
                }

                .form-subtitle {
                    font-size: 1rem;
                    color: #6c757d;
                }

                .form-card {
                    background: #fff;
                    border: 1px solid #e9ecef;
                    border-radius: 12px;
                    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
                    transition: box-shadow 0.3s ease;
                }

                .form-card:hover {
                    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
                }

                .card-header {
                    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
                    border-bottom: 1px solid #e9ecef;
                    padding: 15px 20px;
                    border-radius: 12px 12px 0 0;
                }

                .card-title {
                    color: #495057;
                    font-weight: 600;
                }

                .card-body {
                    padding: 20px;
                }

                .form-label {
                    color: #495057;
                    margin-bottom: 8px;
                }

                .form-control, .form-select {
                    border: 2px solid #e9ecef;
                    border-radius: 8px;
                    padding: 12px 16px;
                    transition: all 0.3s ease;
                }

                .form-control:focus, .form-select:focus {
                    border-color: #dc3545;
                    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
                }

                .form-control-lg {
                    font-size: 1rem;
                    padding: 14px 18px;
                }

                .classification-options {
                    display: flex;
                    flex-direction: column;
                    gap: 15px;
                }

                .form-check-custom {
                    border: 2px solid #e9ecef;
                    border-radius: 8px;
                    padding: 15px;
                    transition: all 0.3s ease;
                }

                .form-check-custom:hover {
                    border-color: #dc3545;
                    background-color: #f8f9fa;
                }

                .form-check-custom input:checked + label {
                    color: #dc3545;
                }

                .severity-badge {
                    display: inline-block;
                    padding: 4px 12px;
                    border-radius: 20px;
                    font-weight: 600;
                    font-size: 0.875rem;
                }

                .severity-minor {
                    background-color: #fff3cd;
                    color: #856404;
                    border: 1px solid #ffeaa7;
                }

                .severity-major {
                    background-color: #f8d7da;
                    color: #721c24;
                    border: 1px solid #f5c6cb;
                }

                .upload-zone {
                    border: 2px dashed #e9ecef;
                    border-radius: 8px;
                    transition: border-color 0.3s ease;
                }

                .upload-zone:hover {
                    border-color: #dc3545;
                }

                .processing-indicator {
                    background-color: #e3f2fd;
                    border: 1px solid #2196f3;
                    border-radius: 8px;
                    padding: 12px 16px;
                    margin-bottom: 16px;
                    color: #1976d2;
                    font-weight: 500;
                    text-align: center;
                }

                .mdi-spin {
                    animation: spin 1s linear infinite;
                }

                @keyframes spin {
                    0% { transform: rotate(0deg); }
                    100% { transform: rotate(360deg); }
                }

                .form-text {
                    margin-top: 8px;
                }

                .text-danger {
                    color: #dc3545 !important;
                }

                .text-muted {
                    color: #6c757d !important;
                }

                .fw-semibold {
                    font-weight: 600;
                }

                .me-1 { margin-right: 0.25rem; }
                .me-2 { margin-right: 0.5rem; }
                .ms-2 { margin-left: 0.5rem; }
                .mb-0 { margin-bottom: 0; }
                .mb-2 { margin-bottom: 0.5rem; }
                .mb-4 { margin-bottom: 1.5rem; }
                .mt-1 { margin-top: 0.25rem; }
                .d-block { display: block; }
            `}</style>
        </div>
    );
};

export default NonConformancesForm;